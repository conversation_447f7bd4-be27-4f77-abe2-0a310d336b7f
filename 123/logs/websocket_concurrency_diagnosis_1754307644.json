{"timestamp": "2025-08-04T13:40:37.704994", "log_analysis": {"gate_exchange.log": {"exchange": "Gate.io", "file_size": 4790, "total_lines": 51, "conflicts": {"recv_conflict_count": 0, "connection_closed_count": 0, "websocket_error_count": 0, "api_limit_count": 0, "symbol_invalid_count": 0, "concurrent_access_count": 0, "health_monitor_count": 0, "connection_pool_count": 0}, "severity": {"severity_level": "LOW", "total_lines": 51, "error_count": 0, "warning_count": 0, "websocket_errors": 0, "error_rate": 0.0}}, "websocket_silent_disconnect_20250804.log": {"exchange": "Unknown", "file_size": 32083, "total_lines": 211, "conflicts": {"recv_conflict_count": 0, "connection_closed_count": 0, "websocket_error_count": 0, "api_limit_count": 0, "symbol_invalid_count": 0, "concurrent_access_count": 0, "health_monitor_count": 0, "connection_pool_count": 0}, "severity": {"severity_level": "LOW", "total_lines": 211, "error_count": 0, "warning_count": 210, "websocket_errors": 0, "error_rate": 0.0}}, "cache_monitor.log": {"exchange": "Unknown", "file_size": 87975, "total_lines": 900, "conflicts": {"recv_conflict_count": 0, "connection_closed_count": 0, "websocket_error_count": 0, "api_limit_count": 0, "symbol_invalid_count": 0, "concurrent_access_count": 0, "health_monitor_count": 0, "connection_pool_count": 0}, "severity": {"severity_level": "LOW", "total_lines": 900, "error_count": 0, "warning_count": 0, "websocket_errors": 0, "error_rate": 0.0}}, "websocket_diagnosis_20250804_122042.log": {"exchange": "OKX", "file_size": 24191, "total_lines": 415, "conflicts": {"recv_conflict_count": 4, "recv_conflict_examples": [{"line_number": 6, "content": "2025-08-04 12:20:42,555 [DIAG] ERROR - [OKX] 接收器2 并发接收异常: cannot call recv while another coroutine is already waiting for the next message"}, {"line_number": 7, "content": "2025-08-04 12:20:42,555 [DIAG] ERROR - [OKX] 接收器3 并发接收异常: cannot call recv while another coroutine is already waiting for the next message"}, {"line_number": 11, "content": "2025-08-04 12:20:42,945 [DIAG] ERROR - ❌ [OKX] 检测到并发recv()冲突: ['cannot call recv while another coroutine is already waiting for the next message', 'cannot call recv while another coroutine is already "}], "connection_closed_count": 0, "websocket_error_count": 0, "api_limit_count": 3, "symbol_invalid_count": 0, "concurrent_access_count": 7, "concurrent_access_examples": [{"line_number": 3, "content": "2025-08-04 12:20:42,069 [DIAG] INFO - 🔍 [OKX] 开始诊断并发访问问题"}, {"line_number": 4, "content": "2025-08-04 12:20:42,069 [DIAG] INFO - [OKX] 测试并发recv()调用"}, {"line_number": 6, "content": "2025-08-04 12:20:42,555 [DIAG] ERROR - [OKX] 接收器2 并发接收异常: cannot call recv while another coroutine is already waiting for the next message"}, {"line_number": 7, "content": "2025-08-04 12:20:42,555 [DIAG] ERROR - [OKX] 接收器3 并发接收异常: cannot call recv while another coroutine is already waiting for the next message"}, {"line_number": 11, "content": "2025-08-04 12:20:42,945 [DIAG] ERROR - ❌ [OKX] 检测到并发recv()冲突: ['cannot call recv while another coroutine is already waiting for the next message', 'cannot call recv while another coroutine is already "}], "health_monitor_count": 0, "connection_pool_count": 1}, "severity": {"severity_level": "MEDIUM", "total_lines": 415, "error_count": 5, "warning_count": 5, "websocket_errors": 0, "error_rate": 1.2048192771084338}}, "execution_engine.log": {"exchange": "Unknown", "file_size": 4937, "total_lines": 69, "conflicts": {"recv_conflict_count": 0, "connection_closed_count": 0, "websocket_error_count": 0, "api_limit_count": 0, "symbol_invalid_count": 0, "concurrent_access_count": 0, "health_monitor_count": 0, "connection_pool_count": 0}, "severity": {"severity_level": "LOW", "total_lines": 69, "error_count": 0, "warning_count": 0, "websocket_errors": 0, "error_rate": 0.0}}, "websocket_subscription_failure_20250804.log": {"exchange": "Unknown", "file_size": 542038, "total_lines": 4628, "conflicts": {"recv_conflict_count": 0, "connection_closed_count": 0, "websocket_error_count": 0, "api_limit_count": 0, "symbol_invalid_count": 0, "concurrent_access_count": 0, "health_monitor_count": 0, "connection_pool_count": 0}, "severity": {"severity_level": "CRITICAL", "total_lines": 4628, "error_count": 4627, "warning_count": 0, "websocket_errors": 0, "error_rate": 99.97839239412272}}, "websocket_prices.log": {"exchange": "OKX", "file_size": 20574756, "total_lines": 125541, "conflicts": {"recv_conflict_count": 0, "connection_closed_count": 0, "websocket_error_count": 0, "api_limit_count": 0, "symbol_invalid_count": 0, "concurrent_access_count": 0, "health_monitor_count": 0, "connection_pool_count": 0}, "severity": {"severity_level": "LOW", "total_lines": 125541, "error_count": 0, "warning_count": 0, "websocket_errors": 0, "error_rate": 0.0}}, "websocket_concurrency_diagnosis.log": {"exchange": "Unknown", "file_size": 1232, "total_lines": 18, "conflicts": {"recv_conflict_count": 5, "recv_conflict_examples": [{"line_number": 3, "content": "2025-08-04 12:40:08,607 [ERROR] ❌ worker_1 recv()失败: cannot call recv while another coroutine is already waiting for the next message"}, {"line_number": 4, "content": "2025-08-04 12:40:08,607 [ERROR] ❌ worker_2 recv()失败: cannot call recv while another coroutine is already waiting for the next message"}, {"line_number": 7, "content": "2025-08-04 12:40:11,614 [ERROR] 健康监控并发冲突: cannot call recv while another coroutine is already waiting for the next message"}, {"line_number": 10, "content": "2025-08-04 12:40:15,612 [ERROR] 连接池管理器并发冲突: cannot call recv while another coroutine is already waiting for the next message"}, {"line_number": 11, "content": "2025-08-04 12:40:15,612 [ERROR] 连接池管理器并发冲突: cannot call recv while another coroutine is already waiting for the next message"}], "connection_closed_count": 0, "websocket_error_count": 0, "api_limit_count": 0, "symbol_invalid_count": 0, "concurrent_access_count": 11, "concurrent_access_examples": [{"line_number": 1, "content": "2025-08-04 12:40:08,606 [INFO] 🔍 开始WebSocket并发冲突诊断..."}, {"line_number": 2, "content": "2025-08-04 12:40:08,606 [INFO] 🧪 测试1: 多协程同时调用recv()"}, {"line_number": 5, "content": "2025-08-04 12:40:10,609 [ERROR] ❌ 检测到WebSocket并发冲突"}, {"line_number": 7, "content": "2025-08-04 12:40:11,614 [ERROR] 健康监控并发冲突: cannot call recv while another coroutine is already waiting for the next message"}, {"line_number": 8, "content": "2025-08-04 12:40:15,612 [ERROR] ❌ 检测到健康监控并发冲突"}], "health_monitor_count": 3, "connection_pool_count": 4}, "severity": {"severity_level": "CRITICAL", "total_lines": 18, "error_count": 9, "warning_count": 0, "websocket_errors": 0, "error_rate": 50.0}}, "error_20250804.log": {"exchange": "OKX", "file_size": 2479663, "total_lines": 31894, "conflicts": {"recv_conflict_count": 3200, "recv_conflict_examples": [{"line_number": 41, "content": "2025-08-04 11:48:09.587 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message"}, {"line_number": 51, "content": "RuntimeError: cannot call recv while another coroutine is already waiting for the next message"}, {"line_number": 52, "content": "2025-08-04 11:48:09.591 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message"}, {"line_number": 62, "content": "RuntimeError: cannot call recv while another coroutine is already waiting for the next message"}, {"line_number": 63, "content": "2025-08-04 11:48:11.179 [ERROR] [websocket] [OKX] WebSocket接收消息异常: cannot call recv while another coroutine is already waiting for the next message"}], "connection_closed_count": 0, "websocket_error_count": 687, "api_limit_count": 8, "symbol_invalid_count": 14, "concurrent_access_count": 0, "health_monitor_count": 0, "connection_pool_count": 0}, "severity": {"severity_level": "CRITICAL", "total_lines": 31894, "error_count": 17586, "warning_count": 0, "websocket_errors": 0, "error_rate": 55.13889759829434}}, "websocket_error_recovery_20250804.log": {"exchange": "OKX", "file_size": 108637, "total_lines": 688, "conflicts": {"recv_conflict_count": 0, "connection_closed_count": 0, "websocket_error_count": 13, "api_limit_count": 0, "symbol_invalid_count": 0, "concurrent_access_count": 0, "health_monitor_count": 0, "connection_pool_count": 0}, "severity": {"severity_level": "CRITICAL", "total_lines": 688, "error_count": 2061, "warning_count": 687, "websocket_errors": 13, "error_rate": 299.5639534883721}}, "bybit_symbol_validation_diagnosis.log": {"exchange": "Bybit", "file_size": 1124, "total_lines": 21, "conflicts": {"recv_conflict_count": 0, "connection_closed_count": 0, "websocket_error_count": 0, "api_limit_count": 0, "symbol_invalid_count": 0, "concurrent_access_count": 0, "health_monitor_count": 0, "connection_pool_count": 0}, "severity": {"severity_level": "LOW", "total_lines": 21, "error_count": 0, "warning_count": 1, "websocket_errors": 0, "error_rate": 0.0}}, "bybit_exchange.log": {"exchange": "Bybit", "file_size": 15550, "total_lines": 149, "conflicts": {"recv_conflict_count": 0, "connection_closed_count": 0, "websocket_error_count": 0, "api_limit_count": 0, "symbol_invalid_count": 11, "concurrent_access_count": 0, "health_monitor_count": 0, "connection_pool_count": 0}, "severity": {"severity_level": "CRITICAL", "total_lines": 149, "error_count": 22, "warning_count": 2, "websocket_errors": 0, "error_rate": 14.76510067114094}}, "OKXExchange.log": {"exchange": "OKX", "file_size": 19357, "total_lines": 129, "conflicts": {"recv_conflict_count": 0, "connection_closed_count": 0, "websocket_error_count": 0, "api_limit_count": 8, "symbol_invalid_count": 0, "concurrent_access_count": 0, "health_monitor_count": 0, "connection_pool_count": 0}, "severity": {"severity_level": "CRITICAL", "total_lines": 129, "error_count": 16, "warning_count": 3, "websocket_errors": 0, "error_rate": 12.4031007751938}}, "websocket_performance_20250804.log": {"exchange": "Unknown", "file_size": 5922091, "total_lines": 23816, "conflicts": {"recv_conflict_count": 0, "connection_closed_count": 0, "websocket_error_count": 0, "api_limit_count": 0, "symbol_invalid_count": 0, "concurrent_access_count": 0, "health_monitor_count": 0, "connection_pool_count": 0}, "severity": {"severity_level": "LOW", "total_lines": 23816, "error_count": 0, "warning_count": 14, "websocket_errors": 0, "error_rate": 0.0}}, "websocket_connection_20250804.log": {"exchange": "Unknown", "file_size": 186, "total_lines": 3, "conflicts": {"recv_conflict_count": 0, "connection_closed_count": 0, "websocket_error_count": 0, "api_limit_count": 0, "symbol_invalid_count": 0, "concurrent_access_count": 0, "health_monitor_count": 0, "connection_pool_count": 0}, "severity": {"severity_level": "LOW", "total_lines": 3, "error_count": 0, "warning_count": 0, "websocket_errors": 0, "error_rate": 0.0}}, "websocket_orderbook_20250804.log": {"exchange": "Unknown", "file_size": 0, "total_lines": 1, "conflicts": {"recv_conflict_count": 0, "connection_closed_count": 0, "websocket_error_count": 0, "api_limit_count": 0, "symbol_invalid_count": 0, "concurrent_access_count": 0, "health_monitor_count": 0, "connection_pool_count": 0}, "severity": {"severity_level": "LOW", "total_lines": 1, "error_count": 0, "warning_count": 0, "websocket_errors": 0, "error_rate": 0.0}}}, "concurrency_conflicts": {}, "severity_assessment": {}, "exchange_comparison": {"OKX": {"log_files_count": 5, "total_recv_conflicts": 3204, "total_concurrent_access": 7, "average_error_rate": 73.66, "severity_assessment": "CRITICAL - 严重并发问题"}, "Gate.io": {"log_files_count": 1, "total_recv_conflicts": 0, "total_concurrent_access": 0, "average_error_rate": 0.0, "severity_assessment": "LOW - 基本正常"}, "Bybit": {"log_files_count": 2, "total_recv_conflicts": 0, "total_concurrent_access": 0, "average_error_rate": 7.38, "severity_assessment": "HIGH - 明显并发问题"}}, "critical_errors": [{"file": "websocket_diagnosis_20250804_122042.log", "exchange": "OKX", "recv_conflicts": 4, "concurrent_access": 7}, {"file": "websocket_concurrency_diagnosis.log", "exchange": "Unknown", "recv_conflicts": 5, "concurrent_access": 11}, {"file": "error_20250804.log", "exchange": "OKX", "recv_conflicts": 3200, "concurrent_access": 0}], "recommendations": ["🚨 优先修复 OKX: 发现 3211 个并发冲突问题", "🔧 OKX 需要禁用健康监控任务，遵守WebSocket单一消费者原则", "🔧 Unknown 需要禁用健康监控任务，遵守WebSocket单一消费者原则", "🔧 OKX 需要禁用健康监控任务，遵守WebSocket单一消费者原则", "🔧 OKX 需要添加交易对验证机制，过滤不支持的交易对", "🔧 Bybit 需要添加交易对验证机制，过滤不支持的交易对"]}