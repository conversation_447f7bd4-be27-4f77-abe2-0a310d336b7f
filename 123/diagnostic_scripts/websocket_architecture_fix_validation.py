#!/usr/bin/env python3
"""
WebSocket架构修复验证脚本
验证WebSocket单一消费者原则和SHIB过滤机制的修复效果
"""

import os
import sys
import json
import time
from pathlib import Path
from typing import Dict, List, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class WebSocketArchitectureFixValidation:
    """WebSocket架构修复验证器"""
    
    def __init__(self):
        self.project_root = project_root
        self.validation_results = {
            "timestamp": int(time.time()),
            "validation_type": "WebSocket架构修复验证",
            "websocket_single_consumer_compliance": {},
            "shib_filtering_mechanism": {},
            "architecture_quality": {},
            "overall_status": "UNKNOWN",
            "recommendations": []
        }
    
    def run_validation(self):
        """运行完整验证"""
        print("🔍 开始WebSocket架构修复验证...")
        
        # 1. 验证WebSocket单一消费者原则合规性
        self._validate_single_consumer_compliance()
        
        # 2. 验证SHIB过滤机制
        self._validate_shib_filtering()
        
        # 3. 验证架构质量
        self._validate_architecture_quality()
        
        # 4. 计算总体状态
        self._calculate_overall_status()
        
        # 5. 保存验证结果
        self._save_validation_results()
        
        return self.validation_results
    
    def _validate_single_consumer_compliance(self):
        """验证WebSocket单一消费者原则合规性"""
        print("📊 验证WebSocket单一消费者原则合规性...")
        
        compliance = {
            "main_loop_recv_only": False,
            "message_queue_implemented": False,
            "websocket_lock_protection": False,
            "concurrent_tasks_safe": False,
            "violations_found": []
        }
        
        # 检查主循环是否是唯一调用recv()的地方
        ws_client_file = self.project_root / "websocket" / "ws_client.py"
        if ws_client_file.exists():
            with open(ws_client_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查主循环recv()调用
            if "await self.ws.recv()" in content and "主循环是唯一调用recv()的地方" in content:
                compliance["main_loop_recv_only"] = True
            
            # 检查消息队列实现
            if "_message_queue" in content and "_unified_message_distributor" in content:
                compliance["message_queue_implemented"] = True
            
            # 检查WebSocket操作锁
            if "_ws_operation_lock" in content and "async with self._ws_operation_lock:" in content:
                compliance["websocket_lock_protection"] = True
            
            # 检查并发任务安全性
            if "通过消息队列" in content and "避免并发" in content:
                compliance["concurrent_tasks_safe"] = True
        
        # 检查各交易所WebSocket实现
        for exchange in ["gate", "bybit", "okx"]:
            ws_file = self.project_root / "websocket" / f"{exchange}_ws.py"
            if ws_file.exists():
                with open(ws_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查是否有违反单一消费者原则的代码
                violations = []
                
                # 检查直接recv()调用（除了继承的run方法）
                if "await self.ws.recv()" in content and exchange != "base":
                    violations.append(f"{exchange}_ws.py中存在直接recv()调用")
                
                # 检查并发监控任务
                if "def _monitor_data_flow(" in content:
                    violations.append(f"{exchange}_ws.py中存在_monitor_data_flow监控任务")
                
                if violations:
                    compliance["violations_found"].extend(violations)
        
        self.validation_results["websocket_single_consumer_compliance"] = compliance
    
    def _validate_shib_filtering(self):
        """验证SHIB过滤机制"""
        print("🔧 验证SHIB交易对过滤机制...")
        
        filtering = {
            "unified_validator_exists": False,
            "bybit_shib_filtered": False,
            "error_level_optimized": False,
            "graceful_degradation": False,
            "smart_filtering_implemented": {}
        }
        
        # 检查统一交易对验证器
        validator_file = self.project_root / "core" / "unified_symbol_validator.py"
        if validator_file.exists():
            filtering["unified_validator_exists"] = True
            
            with open(validator_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查SHIB过滤逻辑
            if "SHIB" in content and "bybit" in content and "futures" in content:
                filtering["bybit_shib_filtered"] = True
        
        # 检查各交易所的智能过滤实现
        for exchange in ["gate", "bybit", "okx"]:
            ws_file = self.project_root / "websocket" / f"{exchange}_ws.py"
            if ws_file.exists():
                with open(ws_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                smart_filtering = {
                    "error_filtering": False,
                    "debug_level_logging": False,
                    "graceful_skip": False
                }
                
                # 检查错误过滤逻辑
                if exchange == "gate":
                    if "unknown currency pair" in content.lower() and "_log_debug" in content:
                        smart_filtering["error_filtering"] = True
                        smart_filtering["debug_level_logging"] = True
                        smart_filtering["graceful_skip"] = True
                elif exchange == "bybit":
                    if "not found" in content.lower() and "_log_debug" in content:
                        smart_filtering["error_filtering"] = True
                        smart_filtering["debug_level_logging"] = True
                        smart_filtering["graceful_skip"] = True
                elif exchange == "okx":
                    if "doesn't exist" in content.lower() and "_log_debug" in content:
                        smart_filtering["error_filtering"] = True
                        smart_filtering["debug_level_logging"] = True
                        smart_filtering["graceful_skip"] = True
                
                filtering["smart_filtering_implemented"][exchange] = smart_filtering
        
        # 检查整体优化状态
        all_exchanges_optimized = all(
            filtering["smart_filtering_implemented"].get(ex, {}).get("error_filtering", False)
            for ex in ["gate", "bybit", "okx"]
        )
        filtering["error_level_optimized"] = all_exchanges_optimized
        filtering["graceful_degradation"] = all_exchanges_optimized
        
        self.validation_results["shib_filtering_mechanism"] = filtering
    
    def _validate_architecture_quality(self):
        """验证架构质量"""
        print("🏗️ 验证WebSocket架构质量...")
        
        quality = {
            "message_queue_architecture": False,
            "unified_modules_usage": False,
            "error_handling_consistency": False,
            "performance_optimization": False,
            "code_clarity": False
        }
        
        # 检查消息队列架构
        ws_client_file = self.project_root / "websocket" / "ws_client.py"
        if ws_client_file.exists():
            with open(ws_client_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if "_unified_message_distributor" in content and "消息队列" in content:
                quality["message_queue_architecture"] = True
        
        # 检查统一模块使用
        unified_modules_count = 0
        for module in ["unified_connection_pool_manager", "unified_timestamp_processor", "unified_symbol_validator"]:
            module_file = self.project_root / "websocket" / f"{module}.py"
            if not module_file.exists():
                module_file = self.project_root / "core" / f"{module}.py"
            
            if module_file.exists():
                unified_modules_count += 1
        
        if unified_modules_count >= 3:
            quality["unified_modules_usage"] = True
        
        # 检查错误处理一致性
        consistent_error_handling = True
        for exchange in ["gate", "bybit", "okx"]:
            ws_file = self.project_root / "websocket" / f"{exchange}_ws.py"
            if ws_file.exists():
                with open(ws_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if "智能过滤" not in content and "自动过滤" not in content:
                    consistent_error_handling = False
                    break
        
        quality["error_handling_consistency"] = consistent_error_handling
        
        # 检查性能优化
        if quality["message_queue_architecture"] and quality["unified_modules_usage"]:
            quality["performance_optimization"] = True
        
        # 检查代码清晰度
        if quality["error_handling_consistency"] and quality["unified_modules_usage"]:
            quality["code_clarity"] = True
        
        self.validation_results["architecture_quality"] = quality
    
    def _calculate_overall_status(self):
        """计算总体状态"""
        compliance = self.validation_results["websocket_single_consumer_compliance"]
        filtering = self.validation_results["shib_filtering_mechanism"]
        quality = self.validation_results["architecture_quality"]
        
        # 计算合规性得分
        compliance_score = sum([
            compliance["main_loop_recv_only"],
            compliance["message_queue_implemented"],
            compliance["websocket_lock_protection"],
            compliance["concurrent_tasks_safe"],
            len(compliance["violations_found"]) == 0
        ])
        
        # 计算过滤机制得分
        filtering_score = sum([
            filtering["unified_validator_exists"],
            filtering["bybit_shib_filtered"],
            filtering["error_level_optimized"],
            filtering["graceful_degradation"]
        ])
        
        # 计算架构质量得分
        quality_score = sum([
            quality["message_queue_architecture"],
            quality["unified_modules_usage"],
            quality["error_handling_consistency"],
            quality["performance_optimization"],
            quality["code_clarity"]
        ])
        
        total_score = compliance_score + filtering_score + quality_score
        max_score = 5 + 4 + 5  # 14
        
        if total_score >= 12:
            self.validation_results["overall_status"] = "EXCELLENT"
        elif total_score >= 10:
            self.validation_results["overall_status"] = "GOOD"
        elif total_score >= 8:
            self.validation_results["overall_status"] = "ACCEPTABLE"
        else:
            self.validation_results["overall_status"] = "NEEDS_IMPROVEMENT"
        
        self.validation_results["score_details"] = {
            "compliance_score": f"{compliance_score}/5",
            "filtering_score": f"{filtering_score}/4", 
            "quality_score": f"{quality_score}/5",
            "total_score": f"{total_score}/{max_score}"
        }
    
    def _save_validation_results(self):
        """保存验证结果"""
        timestamp = int(time.time())
        filename = f"websocket_architecture_fix_validation_{timestamp}.json"
        filepath = self.project_root / "diagnostic_scripts" / filename
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(self.validation_results, f, indent=2, ensure_ascii=False)
        
        print(f"📄 验证结果已保存到: {filename}")
    
    def print_summary(self):
        """打印验证摘要"""
        print("\n" + "="*60)
        print("🔍 WebSocket架构修复验证摘要")
        print("="*60)
        
        overall_status = self.validation_results["overall_status"]
        score_details = self.validation_results["score_details"]
        
        status_emoji = {
            "EXCELLENT": "🏆",
            "GOOD": "✅", 
            "ACCEPTABLE": "⚠️",
            "NEEDS_IMPROVEMENT": "❌"
        }
        
        print(f"{status_emoji.get(overall_status, '❓')} 总体状态: {overall_status}")
        print(f"📊 详细得分:")
        print(f"  - WebSocket合规性: {score_details['compliance_score']}")
        print(f"  - SHIB过滤机制: {score_details['filtering_score']}")
        print(f"  - 架构质量: {score_details['quality_score']}")
        print(f"  - 总分: {score_details['total_score']}")
        
        # 显示关键验证结果
        compliance = self.validation_results["websocket_single_consumer_compliance"]
        if compliance["violations_found"]:
            print(f"\n⚠️ 发现违规: {len(compliance['violations_found'])}个")
            for violation in compliance["violations_found"]:
                print(f"  - {violation}")
        else:
            print(f"\n✅ WebSocket单一消费者原则: 完全合规")
        
        filtering = self.validation_results["shib_filtering_mechanism"]
        if filtering["bybit_shib_filtered"] and filtering["error_level_optimized"]:
            print(f"✅ SHIB过滤机制: 完美实现")
        else:
            print(f"⚠️ SHIB过滤机制: 需要改进")
        
        print("\n" + "="*60)

def main():
    """主函数"""
    validation = WebSocketArchitectureFixValidation()
    results = validation.run_validation()
    validation.print_summary()
    
    return results

if __name__ == "__main__":
    main()
